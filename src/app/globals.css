@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
  /* 优化移动端滚动 */
  -webkit-overflow-scrolling: touch;
  /* 防止移动端缩放 */
  touch-action: manipulation;
}

/* 移动端优化 */
@media (max-width: 768px) {
  /* 优化触摸目标大小 */
  button,
  a,
  input,
  select,
  textarea {
    min-height: 44px;
    min-width: 44px;
  }

  /* 优化表格在移动端的显示 */
  .table-responsive {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }

  /* 优化模态框在移动端的显示 */
  .modal-mobile {
    margin: 0;
    max-height: 100vh;
    border-radius: 0;
  }
}

/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
  /* 移除hover效果，使用active状态 */
  .hover-touch:hover {
    background-color: initial;
  }

  .hover-touch:active {
    background-color: rgba(0, 0, 0, 0.1);
  }
}

/* 高DPI屏幕优化 */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  /* 确保图标和图片在高DPI屏幕上清晰 */
  svg, img {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }
}

/* 自定义滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 移动端安全区域适配 */
@supports (padding: max(0px)) {
  .safe-area-inset-top {
    padding-top: max(1rem, env(safe-area-inset-top));
  }

  .safe-area-inset-bottom {
    padding-bottom: max(1rem, env(safe-area-inset-bottom));
  }

  .safe-area-inset-left {
    padding-left: max(1rem, env(safe-area-inset-left));
  }

  .safe-area-inset-right {
    padding-right: max(1rem, env(safe-area-inset-right));
  }
}
